// This script is working!
// Read this to learn to generate a temporary key: https://www.braintrust.dev/docs/guides/proxy#issue-temporary-credential-in-browser

const API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJicmFpbnRydXN0X3Byb3h5IiwiYXVkIjoiYnJhaW50cnVzdF9wcm94eSIsImp0aSI6ImJ0X3RtcDozYjE3MDE0MS02MzQyLTQ0NjgtODk3Zi0yY2UyNDYzNGIyYzgiLCJidCI6eyJtb2RlbCI6ImdwdC00by1taW5pIiwic2VjcmV0IjoiS3FPNDZBWFRyeEo5eVMxQ2JxenNwTytqWEhlVGIwMUtNdkZwM2p2OE5sMD0ifSwiaWF0IjoxNzUyMjU5NjA0LCJleHAiOjE3NTIyNjAyMDR9.9dlb_KlT5o5zpMR943ujavMdPba4DWz2y-EB9xJiU_Q";

async function main() {
  try {
    const response = await fetch('/api/proxy/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        apiKey: API_KEY,
        model: "gpt-4o-mini",
        messages: [{ role: "user", content: "What is a proxy?" }],
      }),
    });

    const data = await response.json();
    console.log(data.choices[0].message.content);
  } catch (error) {
    console.error('Error:', error);
  }
}

main();



// async function main() {
//     const openai = new OpenAI({
//     baseURL: 'https://api.braintrust.dev/v1/proxy',
//     apiKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJicmFpbnRydXN0X3Byb3h5IiwiYXVkIjoiYnJhaW50cnVzdF9wcm94eSIsImp0aSI6ImJ0X3RtcDoyYTFjOGIzMy01YWI4LTRjZWUtYTI5OC05Mzg0MWI1MzI4MWQiLCJidCI6eyJtb2RlbCI6ImdwdC00LjEiLCJzZWNyZXQiOiJ5RElVUjh3U2c1dThDUDQ5RTNMc3pxUm1kSkJLeW5zeDhKTWtjNjcrNjYwPSJ9LCJpYXQiOjE3NTIwODA3ODYsImV4cCI6MTc1MjA4MTM4Nn0.7plQEgrB9-N1jUCjr9sowPzlxBkml8Hi1nNDkcYUUA4',
//     dangerouslyAllowBrowser: true,
//     });
//     const response = await openai.chat.completions.create({
//         model: 'gpt-4.1',
//         messages: [
//             {
//                 role: 'user',
//                 content: 'Tell me a short story.',
//             },
//         ],
//         stream: true,
//     });
//     console.log(response)
// }

// main();